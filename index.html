<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激光切割设备集尘系统产品迭代对比</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 自定义样式 */
        .version-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        
        .version-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .v1 { border-left-color: #ef4444; }
        .v2 { border-left-color: #f97316; }
        .v3 { border-left-color: #eab308; }
        .v31 { border-left-color: #84cc16; }
        .v4 { border-left-color: #22c55e; }
        
        .rating-stars {
            display: flex;
            gap: 2px;
        }
        
        .star {
            color: #d1d5db;
            transition: color 0.2s;
        }
        
        .star.filled {
            color: #fbbf24;
        }
        
        .feature-tag {
            transition: all 0.2s ease;
        }
        
        .feature-tag:hover {
            transform: scale(1.05);
        }
        
        .advantage {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            border-left: 3px solid #22c55e;
        }
        
        .disadvantage {
            background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
            border-left: 3px solid #ef4444;
        }
        
        .detail-panel {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .detail-panel.expanded {
            max-height: 500px;
        }
        
        @media (max-width: 768px) {
            .comparison-table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }
            
            .version-card {
                min-width: 300px;
                display: inline-block;
                vertical-align: top;
                white-space: normal;
                margin-right: 1rem;
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 页面标题 -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="text-center">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    <i class="fas fa-industry text-blue-600 mr-3"></i>
                    激光切割设备集尘系统产品迭代对比
                </h1>
                <p class="text-gray-600">从V1.0到V4.0的技术演进历程</p>
            </div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 对比表格容器 -->
        <div class="comparison-table flex flex-col lg:flex-row gap-6 overflow-x-auto pb-4">
            <!-- V1.0 版本卡片 -->
            <div class="version-card v1 bg-white rounded-lg shadow-md p-6 flex-1 min-w-80">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">V1.0</h2>
                    <div class="rating-stars" data-rating="2">
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <!-- 连接件设计 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-puzzle-piece text-blue-500 mr-2"></i>连接件设计
                        </h3>
                        <div class="feature-tag bg-gray-100 px-3 py-2 rounded-lg text-sm">
                            长方体盒状设计
                        </div>
                    </div>
                    
                    <!-- 集尘管设计 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-pipe text-purple-500 mr-2"></i>集尘管设计
                        </h3>
                        <div class="feature-tag bg-gray-100 px-3 py-2 rounded-lg text-sm">
                            管路弯折
                        </div>
                    </div>
                    
                    <!-- 主要不足 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>主要问题
                        </h3>
                        <div class="space-y-2">
                            <div class="disadvantage px-3 py-2 rounded-lg text-sm">
                                连接处流速骤变，涡流堆积严重
                            </div>
                            <div class="disadvantage px-3 py-2 rounded-lg text-sm">
                                气流损失显著，抽尘效果差
                            </div>
                            <div class="disadvantage px-3 py-2 rounded-lg text-sm">
                                管路破损漏风问题
                            </div>
                        </div>
                    </div>
                </div>
                
                <button class="mt-4 w-full bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors" 
                        onclick="toggleDetails('v1')">
                    <i class="fas fa-chevron-down mr-2"></i>查看详情
                </button>
                
                <div id="v1-details" class="detail-panel mt-4">
                    <div class="bg-gray-50 p-4 rounded-lg text-sm text-gray-600">
                        <p><strong>技术特点：</strong>初代设计，采用传统的长方体连接方式，结构简单但存在明显的气流动力学缺陷。</p>
                        <p class="mt-2"><strong>应用场景：</strong>早期验证阶段，主要用于概念验证。</p>
                    </div>
                </div>
            </div>

            <!-- V2.0 版本卡片 -->
            <div class="version-card v2 bg-white rounded-lg shadow-md p-6 flex-1 min-w-80">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">V2.0</h2>
                    <div class="rating-stars" data-rating="3">
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <!-- 连接件设计 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-puzzle-piece text-blue-500 mr-2"></i>连接件设计
                        </h3>
                        <div class="feature-tag bg-blue-100 px-3 py-2 rounded-lg text-sm">
                            波纹圆管弯头拼接
                        </div>
                    </div>
                    
                    <!-- 集尘管设计 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-pipe text-purple-500 mr-2"></i>集尘管设计
                        </h3>
                        <div class="feature-tag bg-gray-100 px-3 py-2 rounded-lg text-sm">
                            管路弯折
                        </div>
                    </div>
                    
                    <!-- 主要优点 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>主要优点
                        </h3>
                        <div class="space-y-2">
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                圆管连接流速变化平缓
                            </div>
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                极大减少气流损失
                            </div>
                        </div>
                    </div>
                    
                    <!-- 主要不足 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>待改进
                        </h3>
                        <div class="space-y-2">
                            <div class="disadvantage px-3 py-2 rounded-lg text-sm">
                                波纹管内壁污垢堆积
                            </div>
                            <div class="disadvantage px-3 py-2 rounded-lg text-sm">
                                金属材质存在静电风险
                            </div>
                        </div>
                    </div>
                </div>
                
                <button class="mt-4 w-full bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors" 
                        onclick="toggleDetails('v2')">
                    <i class="fas fa-chevron-down mr-2"></i>查看详情
                </button>
                
                <div id="v2-details" class="detail-panel mt-4">
                    <div class="bg-gray-50 p-4 rounded-lg text-sm text-gray-600">
                        <p><strong>技术突破：</strong>首次采用圆管设计，显著改善了气流动力学性能。</p>
                        <p class="mt-2"><strong>材质选择：</strong>金属波纹管，耐用性好但存在静电隐患。</p>
                    </div>
                </div>
            </div>

            <!-- V3.0 版本卡片 -->
            <div class="version-card v3 bg-white rounded-lg shadow-md p-6 flex-1 min-w-80">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">V3.0</h2>
                    <div class="rating-stars" data-rating="4">
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                    </div>
                </div>

                <div class="space-y-4">
                    <!-- 连接件设计 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-puzzle-piece text-blue-500 mr-2"></i>连接件设计
                        </h3>
                        <div class="feature-tag bg-green-100 px-3 py-2 rounded-lg text-sm">
                            一体式弯管（统一直径）
                        </div>
                    </div>

                    <!-- 集尘管设计 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-pipe text-purple-500 mr-2"></i>集尘管设计
                        </h3>
                        <div class="feature-tag bg-green-100 px-3 py-2 rounded-lg text-sm">
                            直管流通
                        </div>
                    </div>

                    <!-- 主要优点 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>主要优点
                        </h3>
                        <div class="space-y-2">
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                3D打印一体成型
                            </div>
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                内壁光滑污垢不易堆积
                            </div>
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                PLA材质无静电隐患
                            </div>
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                增大流动气流
                            </div>
                        </div>
                    </div>

                    <!-- 主要不足 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>待改进
                        </h3>
                        <div class="space-y-2">
                            <div class="disadvantage px-3 py-2 rounded-lg text-sm">
                                统一直径流速较小
                            </div>
                            <div class="disadvantage px-3 py-2 rounded-lg text-sm">
                                3D打印材质强度有限
                            </div>
                        </div>
                    </div>
                </div>

                <button class="mt-4 w-full bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                        onclick="toggleDetails('v3')">
                    <i class="fas fa-chevron-down mr-2"></i>查看详情
                </button>

                <div id="v3-details" class="detail-panel mt-4">
                    <div class="bg-gray-50 p-4 rounded-lg text-sm text-gray-600">
                        <p><strong>设计革新：</strong>采用3D打印技术实现一体化设计，解决了污垢堆积和静电问题。</p>
                        <p class="mt-2"><strong>材质升级：</strong>PLA材质安全环保，但强度仅适合验证阶段。</p>
                    </div>
                </div>
            </div>

            <!-- V3.1 版本卡片 -->
            <div class="version-card v31 bg-white rounded-lg shadow-md p-6 flex-1 min-w-80">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">V3.1</h2>
                    <div class="rating-stars" data-rating="4">
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                    </div>
                </div>

                <div class="space-y-4">
                    <!-- 连接件设计 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-puzzle-piece text-blue-500 mr-2"></i>连接件设计
                        </h3>
                        <div class="feature-tag bg-green-100 px-3 py-2 rounded-lg text-sm">
                            一体式弯管（过渡变径）
                        </div>
                    </div>

                    <!-- 集尘管设计 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-pipe text-purple-500 mr-2"></i>集尘管设计
                        </h3>
                        <div class="feature-tag bg-green-100 px-3 py-2 rounded-lg text-sm">
                            直管流通
                        </div>
                    </div>

                    <!-- 主要优点 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>主要优点
                        </h3>
                        <div class="space-y-2">
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                最大化流动风速
                            </div>
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                避免能量损失
                            </div>
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                提升集尘能力
                            </div>
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                继承V3.0全部优点
                            </div>
                        </div>
                    </div>

                    <!-- 主要不足 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>待改进
                        </h3>
                        <div class="space-y-2">
                            <div class="disadvantage px-3 py-2 rounded-lg text-sm">
                                3D打印材质强度有限
                            </div>
                        </div>
                    </div>
                </div>

                <button class="mt-4 w-full bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                        onclick="toggleDetails('v31')">
                    <i class="fas fa-chevron-down mr-2"></i>查看详情
                </button>

                <div id="v31-details" class="detail-panel mt-4">
                    <div class="bg-gray-50 p-4 rounded-lg text-sm text-gray-600">
                        <p><strong>流体优化：</strong>引入过渡变径设计，在保持V3.0优点的基础上优化了流速分布。</p>
                        <p class="mt-2"><strong>性能提升：</strong>集尘效率显著提升，为工业化生产奠定基础。</p>
                    </div>
                </div>
            </div>

            <!-- V4.0 版本卡片 -->
            <div class="version-card v4 bg-white rounded-lg shadow-md p-6 flex-1 min-w-80">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-bold text-gray-800">V4.0</h2>
                    <div class="rating-stars" data-rating="5">
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                        <i class="fas fa-star star"></i>
                    </div>
                    <div class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-bold">
                        <i class="fas fa-crown mr-1"></i>最新版
                    </div>
                </div>

                <div class="space-y-4">
                    <!-- 连接件设计 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-puzzle-piece text-blue-500 mr-2"></i>连接件设计
                        </h3>
                        <div class="feature-tag bg-green-100 px-3 py-2 rounded-lg text-sm">
                            一体式弯管（过渡变径）
                        </div>
                    </div>

                    <!-- 集尘管设计 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-pipe text-purple-500 mr-2"></i>集尘管设计
                        </h3>
                        <div class="feature-tag bg-green-100 px-3 py-2 rounded-lg text-sm">
                            直管流通
                        </div>
                    </div>

                    <!-- 主要优点 -->
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>主要优点
                        </h3>
                        <div class="space-y-2">
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                抽尘效果显著提升
                            </div>
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                工业级生产标准
                            </div>
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                重新选材与厂商加工
                            </div>
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                继承V3.1全部优点
                            </div>
                            <div class="advantage px-3 py-2 rounded-lg text-sm">
                                减少管路破损频率
                            </div>
                        </div>
                    </div>

                    <!-- 成熟产品标识 -->
                    <div class="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg border border-green-200">
                        <div class="flex items-center">
                            <i class="fas fa-medal text-green-600 text-xl mr-3"></i>
                            <div>
                                <h4 class="font-semibold text-green-800">生产就绪版本</h4>
                                <p class="text-sm text-green-600">符合工业级生产标准，性能全面优化</p>
                            </div>
                        </div>
                    </div>
                </div>

                <button class="mt-4 w-full bg-green-100 hover:bg-green-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                        onclick="toggleDetails('v4')">
                    <i class="fas fa-chevron-down mr-2"></i>查看详情
                </button>

                <div id="v4-details" class="detail-panel mt-4">
                    <div class="bg-gray-50 p-4 rounded-lg text-sm text-gray-600">
                        <p><strong>工业化升级：</strong>重新选材并采用专业厂商加工，确保产品质量和耐用性。</p>
                        <p class="mt-2"><strong>性能巅峰：</strong>集成了前期所有技术优势，实现了抽尘效果的显著提升。</p>
                        <p class="mt-2"><strong>生产标准：</strong>完全符合工业级生产要求，可大规模应用。</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术演进时间线 -->
        <div class="mt-12 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">
                <i class="fas fa-timeline text-blue-600 mr-3"></i>技术演进时间线
            </h2>

            <div class="relative">
                <!-- 时间线主线 -->
                <div class="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-red-400 via-yellow-400 to-green-400"></div>

                <!-- 时间线节点 -->
                <div class="space-y-8">
                    <div class="flex items-center">
                        <div class="flex-1 text-right pr-8">
                            <h3 class="font-bold text-gray-800">V1.0 - 概念验证</h3>
                            <p class="text-sm text-gray-600">长方体设计，存在明显缺陷</p>
                        </div>
                        <div class="w-4 h-4 bg-red-500 rounded-full border-4 border-white shadow-lg z-10"></div>
                        <div class="flex-1 pl-8"></div>
                    </div>

                    <div class="flex items-center">
                        <div class="flex-1 pr-8"></div>
                        <div class="w-4 h-4 bg-orange-500 rounded-full border-4 border-white shadow-lg z-10"></div>
                        <div class="flex-1 text-left pl-8">
                            <h3 class="font-bold text-gray-800">V2.0 - 流体优化</h3>
                            <p class="text-sm text-gray-600">圆管设计，减少气流损失</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="flex-1 text-right pr-8">
                            <h3 class="font-bold text-gray-800">V3.0 - 一体化设计</h3>
                            <p class="text-sm text-gray-600">3D打印，解决污垢和静电问题</p>
                        </div>
                        <div class="w-4 h-4 bg-yellow-500 rounded-full border-4 border-white shadow-lg z-10"></div>
                        <div class="flex-1 pl-8"></div>
                    </div>

                    <div class="flex items-center">
                        <div class="flex-1 pr-8"></div>
                        <div class="w-4 h-4 bg-lime-500 rounded-full border-4 border-white shadow-lg z-10"></div>
                        <div class="flex-1 text-left pl-8">
                            <h3 class="font-bold text-gray-800">V3.1 - 变径优化</h3>
                            <p class="text-sm text-gray-600">过渡变径，最大化流速</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="flex-1 text-right pr-8">
                            <h3 class="font-bold text-gray-800">V4.0 - 工业化生产</h3>
                            <p class="text-sm text-gray-600">生产就绪，性能巅峰</p>
                        </div>
                        <div class="w-6 h-6 bg-green-500 rounded-full border-4 border-white shadow-lg z-10 flex items-center justify-center">
                            <i class="fas fa-crown text-white text-xs"></i>
                        </div>
                        <div class="flex-1 pl-8"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 性能对比图表 -->
        <div class="mt-12 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">
                <i class="fas fa-chart-bar text-blue-600 mr-3"></i>性能对比分析
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- 抽尘效果 -->
                <div class="text-center">
                    <h3 class="font-semibold text-gray-700 mb-4">抽尘效果</h3>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V1.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-red-500 h-2 rounded-full" style="width: 30%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V2.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-orange-500 h-2 rounded-full" style="width: 60%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V3.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V3.1</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-lime-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V4.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 材质安全性 -->
                <div class="text-center">
                    <h3 class="font-semibold text-gray-700 mb-4">材质安全性</h3>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V1.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-red-500 h-2 rounded-full" style="width: 40%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V2.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-orange-500 h-2 rounded-full" style="width: 50%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V3.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V3.1</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-lime-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V4.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 生产可行性 -->
                <div class="text-center">
                    <h3 class="font-semibold text-gray-700 mb-4">生产可行性</h3>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V1.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-red-500 h-2 rounded-full" style="width: 70%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V2.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-orange-500 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V3.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 40%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V3.1</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-lime-500 h-2 rounded-full" style="width: 45%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V4.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 95%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 维护便利性 -->
                <div class="text-center">
                    <h3 class="font-semibold text-gray-700 mb-4">维护便利性</h3>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V1.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-red-500 h-2 rounded-full" style="width: 35%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V2.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-orange-500 h-2 rounded-full" style="width: 45%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V3.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full" style="width: 80%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V3.1</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-lime-500 h-2 rounded-full" style="width: 85%"></div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">V4.0</span>
                            <div class="w-16 bg-gray-200 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: 90%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p class="text-gray-300">
                <i class="fas fa-cogs mr-2"></i>
                激光切割设备集尘系统技术演进 | 从概念验证到工业化生产
            </p>
            <p class="text-sm text-gray-400 mt-2">
                技术迭代展示了从V1.0到V4.0的完整演进过程
            </p>
        </div>
    </footer>

    <!-- JavaScript 交互功能 -->
    <script>
        // 初始化评分星星
        function initializeRatings() {
            document.querySelectorAll('.rating-stars').forEach(container => {
                const rating = parseInt(container.dataset.rating);
                const stars = container.querySelectorAll('.star');

                stars.forEach((star, index) => {
                    if (index < rating) {
                        star.classList.add('filled');
                    }
                });
            });
        }

        // 切换详情面板
        function toggleDetails(version) {
            const panel = document.getElementById(`${version}-details`);
            const button = panel.previousElementSibling;
            const icon = button.querySelector('i');

            if (panel.classList.contains('expanded')) {
                panel.classList.remove('expanded');
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
                button.innerHTML = '<i class="fas fa-chevron-down mr-2"></i>查看详情';
            } else {
                // 关闭其他已展开的面板
                document.querySelectorAll('.detail-panel.expanded').forEach(otherPanel => {
                    otherPanel.classList.remove('expanded');
                    const otherButton = otherPanel.previousElementSibling;
                    const otherIcon = otherButton.querySelector('i');
                    otherIcon.classList.remove('fa-chevron-up');
                    otherIcon.classList.add('fa-chevron-down');
                    otherButton.innerHTML = '<i class="fas fa-chevron-down mr-2"></i>查看详情';
                });

                panel.classList.add('expanded');
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
                button.innerHTML = '<i class="fas fa-chevron-up mr-2"></i>收起详情';
            }
        }

        // 添加卡片悬停效果增强
        function initializeCardEffects() {
            document.querySelectorAll('.version-card').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-4px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        }

        // 添加滚动动画
        function initializeScrollAnimations() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            document.querySelectorAll('.version-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        }

        // 添加性能条动画
        function animateProgressBars() {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const progressBars = entry.target.querySelectorAll('[style*="width"]');
                        progressBars.forEach((bar, index) => {
                            const width = bar.style.width;
                            bar.style.width = '0%';
                            setTimeout(() => {
                                bar.style.transition = 'width 1s ease';
                                bar.style.width = width;
                            }, index * 100);
                        });
                    }
                });
            }, { threshold: 0.5 });

            document.querySelectorAll('.grid').forEach(grid => {
                observer.observe(grid);
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeRatings();
            initializeCardEffects();
            initializeScrollAnimations();
            animateProgressBars();

            // 添加平滑滚动
            document.documentElement.style.scrollBehavior = 'smooth';
        });

        // 添加键盘导航支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // ESC键关闭所有展开的详情面板
                document.querySelectorAll('.detail-panel.expanded').forEach(panel => {
                    const version = panel.id.replace('-details', '');
                    toggleDetails(version);
                });
            }
        });
    </script>
</body>
</html>
